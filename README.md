# Welcome!

## Welcome to hREA Developer Docs

Welcome to hREA! Here you'll find all the documentation you need to get up and running with the hREA APIs. While this documentation discusses a set of GraphQL APIs, which could in general have multiple language implementations, all of this documentation is at this time focused on a library written for browser and nodejs based javascript projects.

{% hint style="info" %}
If you're looking for more general information about hREA, check out the main website [https://hrea.io](https://hrea.io)
{% endhint %}

## Want to jump right in?

Feeling like an eager beaver? Jump in to the quick start docs and get making your first request:

{% content-ref url="quick-start.md" %}
[quick-start.md](quick-start.md)
{% endcontent-ref %}

## Want to deep dive?

Dive a little deeper and start exploring our API reference to get an idea of everything that's possible with the API:

{% content-ref url="reference/graphql-api-reference/" %}
[graphql-api-reference](reference/graphql-api-reference/)
{% endcontent-ref %}
