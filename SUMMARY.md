# Table of contents

* [Welcome!](README.md)
* [Quick Start](index.md)
* [Integration Guide](integration-guide.md)
* [Basic Usage with Lit](basic-usage-lit.md)

## Advanced Topics

* [Thinking & Expressing ValueFlows](thinking-and-expressing-valueflows.md)
* [Using "myAgent"](using-myagent.md)
* [Configuring Enabled Modules](configuring-enabled-modules.md)

## Reference

* [GraphQL API Reference](reference/graphql-api-reference/README.md)
  * [Action](reference/graphql-api-reference/action.md)
  * [Agent](reference/graphql-api-reference/agent.md)
  * [AgentRelationship](reference/graphql-api-reference/agent-relationship.md)
  * [AgentRelationshipRole](reference/graphql-api-reference/agent-relationship-role.md)
  * [Agreement](reference/graphql-api-reference/agreement.md)
  * [Appreciation](reference/graphql-api-reference/appreciation.md)
  * [Claim](reference/graphql-api-reference/claim.md)
  * [Commitment](reference/graphql-api-reference/commitment.md)
  * [EconomicEvent](reference/graphql-api-reference/economic-event.md)
  * [EconomicResource](reference/graphql-api-reference/economic-resource.md)
  * [Geolocation](reference/graphql-api-reference/geolocation.md)
  * [Intent](reference/graphql-api-reference/intent.md)
  * [Measurement](reference/graphql-api-reference/measurement.md)
  * [Plan](reference/graphql-api-reference/plan.md)
  * [Process](reference/graphql-api-reference/process.md)
  * [Process Specification](reference/graphql-api-reference/process-specification.md)
  * [Product Batch](reference/graphql-api-reference/product-batch.md)
  * [Proposal](reference/graphql-api-reference/proposal.md)
  * [Recipe](reference/graphql-api-reference/recipe.md)
  * [Resource Specification](reference/graphql-api-reference/resource-specification.md)
  * [Scenario](reference/graphql-api-reference/scenario.md)
  * [Scalars](reference/graphql-api-reference/scalars.md)
  * [Utility Types](reference/graphql-api-reference/utility-types.md)
* [Ship to Production](ship-to-production.md)
