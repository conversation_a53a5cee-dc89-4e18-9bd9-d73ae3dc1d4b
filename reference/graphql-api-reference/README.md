# Graphql API Reference

## Modules

Each of the items listed below is what is known as a Valueflows Module. They can be individually enabled, and so looking at each one specifically will tell you which Graphql queries and mutations are accessible.

- [Action](./action.md)
- [Agent](./agent.md)
- [Agent Relationship](./agent-relationship.md)
- [Agent Relationship Role](./agent-relationship-role.md)
- [Agreement](./agreement.md)
- [Appreciation](./appreciation.md)
- [Claim](./claim.md)
- [Commitment](./commitment.md)
- [Economic Event](./economic-event.md)
- [Economic Resource](./economic-resource.md)
- [Geolocation](./geolocation.md)
- [Intent](./intent.md)
- [Measurement](./measurement.md)
- [Plan](./plan.md)
- [Process](./process.md)
- [Process Specification](./process-specification.md)
- [Product Batch](./product-batch.md)
- [Proposal](./proposal.md)
- [Recipe](./recipe.md)
- [Resource Specification](./resource-specification.md)
- [Scenario](./scenario.md)
- [Scalars](./scalars.md)
- [Utility Types](./utility-types.md)
