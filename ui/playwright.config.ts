import { defineConfig, devices } from '@playwright/test';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: false, // Disable parallel execution for Holochain tests
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 1 : 0, // Reduce retries for faster feedback
  workers: 1, // Single worker to avoid Holochain conductor conflicts
  reporter: [
    ['html'],
    ['list'], // Add list reporter for better console output
    ['junit', { outputFile: 'test-results/junit.xml' }] // For CI integration
  ],
  globalSetup: join(__dirname, 'tests/setup/global-setup.ts'),
  globalTeardown: join(__dirname, 'tests/setup/global-teardown.ts'),
  timeout: 60000, // Increase timeout for Holochain operations
  use: {
    // Testing the app in development mode
    baseURL: process.env.TAURI_DEV ? 'http://localhost:5173' : 'tauri://localhost',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'on-first-retry',
    // Increase timeouts for Holochain operations
    actionTimeout: 15000,
    navigationTimeout: 30000,
  },
  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1280, height: 720 },
        launchOptions: {
          args: ['--no-sandbox']
        }
      },
    }
  ],
  webServer: process.env.TAURI_DEV ? {
    command: 'bun run start',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
    timeout: 120000, // Increase timeout to 2 minutes
  } : undefined,
});
